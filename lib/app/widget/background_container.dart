import 'package:lovey_app/app/core/theme/colors.dart';
import 'package:flutter/material.dart';

class BackgroundContainer extends StatefulWidget {
  final Widget? child;
  final Widget? customBackground;

  const BackgroundContainer({super.key, this.child, this.customBackground});

  @override
  State<BackgroundContainer> createState() => _BackgroundContainer();
}

class _BackgroundContainer extends State<BackgroundContainer> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [CustomColors.infraRed, CustomColors.tulip],
            ),
          ),
        ),
        Opacity(
          opacity: widget.customBackground != null ? 1 : 0,
          child: widget.customBackground ?? SizedBox(),
        ),
        widget.child ?? SizedBox(),
      ],
    );
  }
}
