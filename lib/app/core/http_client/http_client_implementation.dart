import 'package:dio/dio.dart';
import 'package:lovey_app/app/core/http_client/http_client.dart';
import 'package:lovey_app/app/core/http_client/http_response.dart';

class HttpClientImplementation implements HttpClient {
  final client = Dio();

  HttpClientImplementation();

  Future<Map<String, dynamic>> getHeaders(
    Map<String, dynamic>? currentHeaders,
    bool authenticated,
  ) async {
    Map<String, dynamic> headers = currentHeaders ?? {};
    if (!headers.containsKey('Content-Type')) {
      headers['Contents-Type'] = 'application/json';
      headers['Accept'] = 'application/json';
    }
    if (authenticated) {
      // headers['authorization'] = "Bearer ${await _userStorage.getUserToken()}";
    }
    return headers;
  }

  @override
  Future<HttpResponse> get(
    String url, {
    bool authenticated = true,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
  }) async {
    final response = await client.get(
      url,
      options: Options(
        headers: await getHeaders(headers, authenticated),
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: (status) => true,
      ),
      queryParameters: queryParameters,
    );
    return HttpResponse(data: response.data, statusCode: response.statusCode);
  }

  @override
  Future<HttpResponse> post(
    String url, {
    bool authenticated = true,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    dynamic body,
  }) async {
    final response = await client.post(
      url,
      data: body,
      options: Options(
        headers: await getHeaders(headers, authenticated),
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: (status) => true,
      ),
    );
    return HttpResponse(data: response.data, statusCode: response.statusCode);
  }

  @override
  Future<HttpResponse> put(
    String url, {
    bool authenticated = true,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    dynamic body,
  }) async {
    final response = await client.put(
      url,
      data: body,
      options: Options(
        headers: await getHeaders(headers, authenticated),
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: (status) => true,
      ),
    );
    return HttpResponse(data: response.data, statusCode: response.statusCode);
  }

  @override
  Future<HttpResponse> delete(
    String url, {
    bool authenticated = true,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    dynamic body,
  }) async {
    final response = await client.delete(
      url,
      data: body,
      options: Options(
        headers: await getHeaders(headers, authenticated),
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: (status) => true,
      ),
    );
    return HttpResponse(data: null, statusCode: response.statusCode);
  }

  @override
  Future<HttpResponse> patch(
    String url, {
    bool authenticated = true,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    dynamic body,
  }) async {
    final response = await client.patch(
      url,
      data: body,
      options: Options(
        headers: await getHeaders(headers, authenticated),
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: (status) => true,
      ),
    );
    return HttpResponse(data: null, statusCode: response.statusCode);
  }
}
