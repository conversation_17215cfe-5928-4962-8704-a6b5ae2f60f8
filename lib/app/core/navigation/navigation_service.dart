import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'app_routes.dart';
import 'app_router.dart';

/// Serviço para facilitar a navegação em qualquer lugar do app
class NavigationService {
  /// Navega para uma rota específica usando o GoRouter diretamente
  static void goTo(String route, {Object? extra}) {
    AppRouter.router.go(route, extra: extra);
  }

  /// Navega para uma rota e adiciona à pilha
  static void pushTo(String route, {Object? extra}) {
    AppRouter.router.push(route, extra: extra);
  }

  /// Volta para a tela anterior ou vai para home se não houver pilha
  static void goBack() {
    if (AppRouter.router.canPop()) {
      AppRouter.router.pop();
    } else {
      goToHome();
    }
  }

  /// Métodos específicos para cada tela (facilita o uso)
  static void goToHome() => goTo(AppRoutes.home);
  static void goToChat() => goTo(AppRoutes.chat);

  /// Adicione métodos específicos para novas telas aqui
  // static void goToProfile() => goTo(AppRoutes.profile);
  // static void goToSettings() => goTo(AppRoutes.settings);
}
