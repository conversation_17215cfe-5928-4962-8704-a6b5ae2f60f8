import 'package:flutter/material.dart';

Color hexToColor(String colorHex, {String alphaChannel = 'FF'}) {
  return Color(int.parse(colorHex.replaceFirst('#', '0x$alphaChannel')));
}

Color rgbToColor(String rgb) {
  Color res = CustomColors.primary;
  try {
    List<String> parts = rgb
        .replaceFirst('rgb', '')
        .replaceFirst('(', '')
        .replaceFirst(')', '')
        .split(',');
    int red = int.parse(parts[0]);
    int green = int.parse(parts[1]);
    int blue = int.parse(parts[2]);
    res = Color.fromRGBO(red, green, blue, 1);
  } catch (err) {
    debugPrint('rtC=>$err');
  }
  return res;
}

Color stringToColor(String color) {
  if (color.contains('#')) {
    return hexToColor(color);
  }
  return rgbToColor(color);
}

MaterialColor createMaterialColor(Color color) {
  List strengths = <double>[.05];
  Map<int, Color> swatch = {};
  final int r = color.red, g = color.green, b = color.blue;

  for (int i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (var strength in strengths) {
    final double ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  return MaterialColor(color.value, swatch);
}

enum EColors {
  primary,
  secondary,
  accent,
  background,
  surface,
  error,
  text,
  onSurface,
  disabled,
  placeholder,
  backdrop,
  notification,
  lightCrimson,
  wildWatermelon,
  americanPurple,
  charmPink,
  orangeSoda,
  button,
}

// Color getAppColor(EColors eColors) {
//   return colors[eColors] ?? Colors.white;
// }

// MaterialColor getAppMaterialColor(EColors eColors) {
//   return createMaterialColor(colors[eColors] ?? Colors.white);
// }

class CustomColors {
  CustomColors._();
  static const Color primary = Color(0xFF000000);
  static const Color infraRed = Color(0xFFFF466B);
  static const Color tulip = Color(0xFFFF7C97);
  static const Color barnRed = Color(0xFF780303);
}

/** Remember
 * 100% — FF

    95% — F2

    90% — E6

    85% — D9

    80% — CC

    75% — BF

    70% — B3

    65% — A6

    60% — 99

    55% — 8C

    50% — 80

    45% — 73

    40% — 66

    35% — 59

    30% — 4D

    25% — 40

    20% — 33

    15% — 26

    10% — 1A

    5% — 0D

    0% — 00
 */
