class _SvgAssets {
  final String customPath;
  final String format;

  _SvgAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }

  String get loveyMiddle => this["lovey_middle"];
  String get loveyFill => this["lovey_fill"];
  String get loveyHeartNormal => this["lovey_heart_normal"];
  String get bigLoveyContent => this["big_lovey_content"];
  String get bigLoveyNormal => this["big_lovey_normal"];
  String get bigLoveySmiling => this["big_lovey_smiling"];
  String get biggerLoveyNormal => this["bigger_lovey_normal"];
}

class _PngAssets {
  final String customPath;
  final String format;

  _PngAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }

  String get googleLogo => this["google_logo"];
  String get appleLogo => this["apple_logo"];
}

class _RivAssets {
  final String customPath;
  final String format;

  _RivAssets({required this.customPath, required this.format});

  String operator [](String assetName) {
    return '$customPath/$assetName.$format';
  }
}

class Assets {
  static const String _customPath = 'assets';
  Assets._();
  static const envFile = ".env";
  static final svg = _SvgAssets(
    customPath: "$_customPath/img/svg",
    format: 'svg',
  );
  static final png = _PngAssets(
    customPath: "$_customPath/img/png",
    format: 'png',
  );
  static final riv = _RivAssets(
    customPath: "$_customPath/animations/rive",
    format: 'riv',
  );
}
