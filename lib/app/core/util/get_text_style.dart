import 'package:flutter/material.dart';
import 'package:lovey_app/app/core/theme/fonts.enum.dart';

TextStyle getTextStyle(
  double fontSize, {
  TextFontEnum fontFamily = TextFontEnum.albertSans,
  FontWeight fontWeight = FontWeight.w400,
  Color color = Colors.white,
  double? height,
  TextDecoration? decoration,
  double? letterSpacing,
  Color? decorationColor,
}) {
  return TextStyle(
    fontFamily: fontFamily.label,
    fontSize: fontSize,
    height: height,
    color: color,
    letterSpacing: letterSpacing,
    fontWeight: fontWeight,
    decoration: decoration,
    decorationColor: decorationColor,
    fontVariations: <FontVariation>[
      FontVariation('wght', fontWeight.value.toDouble()),
    ],
  );
}
