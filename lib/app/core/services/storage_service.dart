import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _userIdKey = 'user_id';
  
  static StorageService? _instance;
  static SharedPreferences? _preferences;
  
  StorageService._();
  
  static Future<StorageService> getInstance() async {
    _instance ??= StorageService._();
    _preferences ??= await SharedPreferences.getInstance();
    return _instance!;
  }
  
  // Salvar user_id
  Future<bool> saveUserId(int userId) async {
    return await _preferences!.setInt(_userIdKey, userId);
  }
  
  // Obter user_id
  int? getUserId() {
    return _preferences!.getInt(_userIdKey);
  }
  
  // Verificar se user_id existe
  bool hasUserId() {
    return _preferences!.containsKey(_userIdKey);
  }
  
  // Remover user_id
  Future<bool> removeUserId() async {
    return await _preferences!.remove(_userIdKey);
  }
  
  // Limpar todos os dados
  Future<bool> clearAll() async {
    return await _preferences!.clear();
  }
}
