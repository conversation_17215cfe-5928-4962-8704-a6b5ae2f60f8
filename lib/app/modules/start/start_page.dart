import 'package:flutter/material.dart';
import 'package:lovey_app/app/core/navigation/app_routes.dart';
import 'package:lovey_app/app/core/navigation/navigation_service.dart';
import 'package:lovey_app/app/core/util/assets_strings.dart';
import 'package:lovey_app/app/core/util/interval_utils.dart';
import 'package:lovey_app/app/widget/background_container.dart';
import 'package:lovey_app/app/widget/custom_image.dart';

class StartPage extends StatefulWidget {
  const StartPage({super.key});

  @override
  State<StartPage> createState() => _StartPageState();
}

class _StartPageState extends State<StartPage> {
  @override
  void initState() {
    setTimeout(
      callback: () {
        NavigationService.goTo(AppRoutes.helloLovey);
      },
      duration: const Duration(seconds: 3),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundContainer(
      child: Center(child: CustomImage(Assets.svg.loveyHeartNormal)),
    );
  }
}
