import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:lovey_app/app/core/theme/fonts.enum.dart';
import 'package:lovey_app/app/core/util/assets_strings.dart';
import 'package:lovey_app/app/core/util/get_text_style.dart';
import 'package:lovey_app/app/modules/start/controller/hello_lovey_controller.dart';
import 'package:lovey_app/app/widget/background_container.dart';
import 'package:lovey_app/app/widget/custom_image.dart';
import 'package:sizer/sizer.dart';

class HelloLoveyPage extends StatefulWidget {
  const HelloLoveyPage({super.key});

  @override
  State<HelloLoveyPage> createState() => _HelloLoveyPageState();
}

class _HelloLoveyPageState extends State<HelloLoveyPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<double> _newTextSlideAnimation;
  late Animation<double> _newTextFadeAnimation;
  late Animation<double> _loveyToAppBarAnimation;
  late Animation<double> _loveyToAppBarFadeAnimation;
  late Animation<double> _loveyToAppBarScaleAnimation;
  late Animation<double> _finalBodySlideAnimation;
  late Animation<double> _finalBodyFadeAnimation;
  late Animation<double> _imageDownAnimation;
  late Animation<double> _imageTransitionAnimation;
  HelloLoveyController pageController = HelloLoveyController();

  // Controla qual imagem mostrar
  String _currentImage = Assets.svg.bigLoveyNormal;
  String _nextImage = Assets.svg.bigLoveyNormal;

  // Controla a visibilidade dos textos
  bool _showOriginalText = true;
  bool _showNewText = false;
  bool _showLoveyInAppBar = false;
  bool _showFinalBody = false;
  bool _isTransitioningImage = false;

  @override
  void initState() {
    super.initState();

    // Inicializa o controller da animação
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800), // Duração da animação
      vsync: this,
    );

    // Cria a animação de deslizamento
    _slideAnimation = Tween<double>(
      begin: 1.0, // Começa fora da tela (abaixo)
      end: 0.0, // Termina na posição normal
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // Curva suave com um pequeno bounce
      ),
    );

    // Cria a animação de fade (opacidade)
    _fadeAnimation = Tween<double>(
      begin: 0.0, // Começa invisível
      end: 1.0, // Termina totalmente visível
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn, // Fade suave
      ),
    );

    // Cria a animação de escala para o crescimento
    _scaleAnimation = Tween<double>(
      begin: 1.0, // Tamanho normal
      end: 1.0, // Será alterado quando necessário
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Animação de fade out para o texto original
    _textFadeAnimation = Tween<double>(
      begin: 1.0, // Visível
      end: 1.0, // Invisível
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // Inicializa as animações do novo texto com valores que mantêm o texto invisível
    _newTextSlideAnimation = Tween<double>(
      begin: 1.0, // Começa abaixo (fora da tela)
      end: 1.0, // Mantém fora da tela inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    // Animação de fade in para o novo texto (começa e mantém invisível)
    _newTextFadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 0.0, // Mantém invisível inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // Inicializa as animações para mover "lovey" para a AppBar
    _loveyToAppBarAnimation = Tween<double>(
      begin: -0.15, // Posição inicial (centro)
      end: -0.15, // Mantém na posição inicial
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loveyToAppBarFadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 0.0, // Mantém invisível inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _loveyToAppBarScaleAnimation = Tween<double>(
      begin: 1.0, // Tamanho normal
      end: 1.0, // Mantém tamanho normal inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Inicializa as animações para o corpo final
    _finalBodySlideAnimation = Tween<double>(
      begin: 1.0, // Começa abaixo (fora da tela)
      end: 1.0, // Mantém fora da tela inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _finalBodyFadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 0.0, // Mantém invisível inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // Inicializa a animação para a imagem descer
    _imageDownAnimation = Tween<double>(
      begin: 0.0, // Posição inicial
      end: 0.0, // Mantém na posição inicial
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Inicializa a animação de transição da imagem
    _imageTransitionAnimation = Tween<double>(
      begin: 0.0, // Imagem atual totalmente visível
      end: 0.0, // Mantém visível inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Inicia a animação após 3 segundos
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _animationController.forward();
        _startImageSequence();
      }
    });
  }

  void _startImageSequence() {
    // Após 1 segundo: muda para bigLoveySmiling
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _currentImage = Assets.svg.bigLoveySmiling;
        });
      }
    });

    // Após mais 1 segundo (2 segundos total): muda para bigLoveyContent e inicia animação de texto
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentImage = Assets.svg.bigLoveyContent;
        });
        _startTextAnimation();
      }
    });

    // Após mais 2 segundos (6 segundos total): inicia animação do "lovey" para AppBar
    Future.delayed(const Duration(seconds: 6), () {
      if (mounted) {
        _startLoveyToAppBarAnimation();
      }
    });

    // Após mais 3 segundos (9 segundos total): inicia a nova sequência final
    Future.delayed(const Duration(seconds: 9), () {
      if (mounted) {
        _startFinalSequence();
      }
    });
  }

  void _startTextAnimation() {
    // Fade out rápido do texto original
    final textFadeController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );

    final textFadeOut = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: textFadeController, curve: Curves.easeOut),
    );

    textFadeOut.addListener(() {
      setState(() {
        _textFadeAnimation = textFadeOut;
      });
    });

    textFadeController.forward().then((_) {
      // Após o fade out, esconde o texto original
      setState(() {
        _showOriginalText = false;
      });

      // Pequeno delay antes de mostrar o novo texto para evitar o "piscar"
      Future.delayed(const Duration(milliseconds: 50), () {
        if (mounted) {
          setState(() {
            _showNewText = true;
          });
          // Inicia a animação do novo texto
          _startNewTextAnimation();
        }
      });

      textFadeController.dispose();
    });
  }

  void _startNewTextAnimation() {
    // Animação do novo texto subindo
    final newTextController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    final slideAnimation = Tween<double>(
      begin: 1.0, // Começa abaixo (fora da tela)
      end: -0.15, // Termina 15% acima da posição central
    ).animate(
      CurvedAnimation(parent: newTextController, curve: Curves.easeOutBack),
    );

    final fadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 1.0, // Visível
    ).animate(
      CurvedAnimation(
        parent: newTextController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    // Atualiza as animações antes de iniciar
    setState(() {
      _newTextSlideAnimation = slideAnimation;
      _newTextFadeAnimation = fadeAnimation;
    });

    // Adiciona listener para atualizar o estado durante a animação
    newTextController.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    newTextController.forward().then((_) {
      newTextController.dispose();
    });
  }

  void _startLoveyToAppBarAnimation() {
    // Primeiro, mostra o "lovey" na posição original
    setState(() {
      _currentImage = Assets.svg.biggerLoveyNormal;
      _showLoveyInAppBar = true;
    });

    // Pequeno delay para garantir que o widget seja renderizado
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!mounted) return;

      // Cria um controller específico para a animação do "lovey" para AppBar
      final loveyToAppBarController = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );

      // Calcula a distância do centro da tela até a posição da AppBar
      final screenHeight = MediaQuery.of(context).size.height;
      final appBarHeight = kToolbarHeight + MediaQuery.of(context).padding.top;

      // Posição inicial: centro da tela (onde está o texto "lovey" original)
      final centerY = screenHeight / 2;
      // Posição final: AppBar
      final appBarY = appBarHeight + 16; // 16px de padding
      final moveDistance = centerY - appBarY;

      final slideToAppBarAnimation = Tween<double>(
        begin: 0.0, // Posição inicial (centro)
        end: -moveDistance, // Move para cima até a AppBar
      ).animate(
        CurvedAnimation(
          parent: loveyToAppBarController,
          curve: Curves.easeInOutBack,
        ),
      );

      final fadeToAppBarAnimation = Tween<double>(
        begin: 1.0, // Visível
        end: 1.0, // Mantém visível durante a animação
      ).animate(
        CurvedAnimation(parent: loveyToAppBarController, curve: Curves.easeIn),
      );

      final scaleToAppBarAnimation = Tween<double>(
        begin: 1.0, // Tamanho original (66.18)
        end: 0.48, // Tamanho da AppBar (32 / 66.18 ≈ 0.48)
      ).animate(
        CurvedAnimation(
          parent: loveyToAppBarController,
          curve: Curves.easeInOut,
        ),
      );

      // Atualiza as animações
      setState(() {
        _loveyToAppBarAnimation = slideToAppBarAnimation;
        _loveyToAppBarFadeAnimation = fadeToAppBarAnimation;
        _loveyToAppBarScaleAnimation = scaleToAppBarAnimation;
      });

      // Adiciona listener para atualizar o estado durante a animação
      loveyToAppBarController.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      loveyToAppBarController.forward().then((_) {
        loveyToAppBarController.dispose();
      });
    });
  }

  void _startFinalSequence() {
    // Aguarda 3 segundos após "Got feels? Ask me!" aparecer
    Future.delayed(const Duration(seconds: 3, milliseconds: 500), () {
      if (!mounted) return;

      // Cria um controller para a sequência final
      final finalSequenceController = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );

      // Animação de fade out para "Got feels? Ask me!" (400ms)
      final textFadeOutController = AnimationController(
        duration: const Duration(milliseconds: 400),
        vsync: this,
      );

      final textFadeOut = Tween<double>(begin: 1.0, end: 0.0).animate(
        CurvedAnimation(parent: textFadeOutController, curve: Curves.easeOut),
      );

      // Animação para o corpo final subir para o centro
      final finalBodySlideUp = Tween<double>(
        begin: 1.0, // Começa abaixo (fora da tela)
        end: 0.0, // Termina no centro
      ).animate(
        CurvedAnimation(
          parent: finalSequenceController,
          curve: Curves.easeOutBack,
        ),
      );

      final finalBodyFadeIn = Tween<double>(
        begin: 0.0, // Invisível
        end: 1.0, // Visível
      ).animate(
        CurvedAnimation(parent: finalSequenceController, curve: Curves.easeIn),
      );

      // Animação para a imagem descer até a altura de bigLoveySmiling
      // final imageDownMove = Tween<double>(
      //   begin: 0.0, // Posição atual (biggerLoveyNormal)
      //   end:
      //       77.0, // Desce para a altura de bigLoveySmiling (diferença de altura entre as imagens)
      // ).animate(
      //   CurvedAnimation(
      //     parent: finalSequenceController,
      //     curve: Curves.easeInOut,
      //   ),
      // );

      // Inicia o fade out do texto "Got feels? Ask me!"
      textFadeOutController.forward().then((_) {
        // Após o fade out do texto, mostra o corpo final e inicia as animações simultâneas
        setState(() {
          _showFinalBody = true;
          _finalBodySlideAnimation = finalBodySlideUp;
          _finalBodyFadeAnimation = finalBodyFadeIn;
          // _imageDownAnimation = imageDownMove;
        });

        // Inicia a transição suave da imagem SIMULTANEAMENTE com as outras animações
        _startImageTransition(Assets.svg.bigLoveySmiling);

        // Inicia as animações simultâneas
        finalSequenceController.forward().then((_) {
          finalSequenceController.dispose();
        });

        textFadeOutController.dispose();
      });

      // Adiciona listeners para atualizar o estado durante as animações
      textFadeOutController.addListener(() {
        if (mounted) {
          setState(() {
            // Atualiza o fade do texto "Got feels? Ask me!"
            _newTextFadeAnimation = textFadeOut;
          });
        }
      });

      finalSequenceController.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });
    });
  }

  void _startImageTransition(String newImage) {
    // Cria um controller para a transição da imagem
    final imageTransitionController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Animação de fade out da imagem atual e fade in da nova imagem
    final imageTransition = Tween<double>(
      begin: 0.0, // Imagem atual totalmente visível
      end: 1.0, // Nova imagem totalmente visível
    ).animate(
      CurvedAnimation(
        parent: imageTransitionController,
        curve: Curves.easeInOut,
      ),
    );

    // Define a próxima imagem
    setState(() {
      _nextImage = newImage;
      _isTransitioningImage = true;
      _imageTransitionAnimation = imageTransition;
    });

    // Adiciona listener para atualizar o estado durante a animação
    imageTransitionController.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    // Inicia a transição
    imageTransitionController.forward().then((_) {
      // Após a transição, atualiza a imagem atual e limpa a transição
      setState(() {
        _currentImage = newImage;
        _isTransitioningImage = false;
      });
      imageTransitionController.dispose();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildSocialButtons() {
    return Container(
      constraints: BoxConstraints(maxWidth: 500),
      width: 83.w,
      child: Column(
        children: [
          // Botão Google
          _buildSocialButton(
            onPressed: () {
              // TODO: Implementar login com Google
            },
            iconAsset: Assets.png.googleLogo,
            text: 'Sign in with Google',
          ),
          SizedBox(height: 2.h),
          // Botão Apple
          _buildSocialButton(
            onPressed: () {
              // TODO: Implementar login com Apple
            },
            iconAsset: Assets.png.appleLogo,
            text: 'Sign in with Apple',
            isGoogle: false,
          ),
          SizedBox(height: 2.4.h),
          // Botão "Sign in without account"
          TextButton(
            onPressed: () {
              // TODO: Implementar login sem conta
            },
            child: Text(
              'Sign in without account',
              style: getTextStyle(
                14,
                fontWeight: FontWeight.w500,
                fontFamily: TextFontEnum.poppins,
                color: Colors.white,
                decoration: TextDecoration.underline,
                decorationColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButton({
    required VoidCallback onPressed,
    required String iconAsset,
    required String text,
    bool isGoogle = true,
  }) {
    final imageSize = isGoogle ? 24.0 : 27.0;
    return Container(
      width: double.infinity,
      height: 56,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
            side: BorderSide.none,
          ),
          padding: EdgeInsets.symmetric(horizontal: 6.w),
        ),
        child: Row(
          children: [
            // Ícone
            SizedBox(
              width: imageSize,
              height: imageSize,
              child: Image.asset(
                iconAsset,
                width: imageSize,
                height: imageSize,
                fit: BoxFit.contain,
              ),
            ),
            // Espaço flexível para centralizar o texto
            Expanded(
              child: Center(
                child: Text(
                  text,
                  style: getTextStyle(
                    16,
                    fontWeight: FontWeight.w600,
                    fontFamily: TextFontEnum.poppins,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            // Espaço invisível para balancear o ícone
            SizedBox(width: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildFinalCenterBody() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Sign in to begin!',
          style: getTextStyle(
            30,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.5,
            fontFamily: TextFontEnum.poppins,
          ),
        ),
        SizedBox(height: 6.h),
        Center(
          // width: 300,
          child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              text: 'By signing in, you agree to our ',
              style: getTextStyle(
                14,

                letterSpacing: -0.4,
                fontWeight: FontWeight.w400,
                fontFamily: TextFontEnum.poppins,
              ),
              children: <TextSpan>[
                TextSpan(
                  text: 'Terms of Use\n',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.bold,
                    fontFamily: TextFontEnum.poppins,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          // Ação quando o texto "TERMS OF USE" for clicado
                          pageController.launchPageOfTermsOfUse();
                        },
                ),
                TextSpan(
                  text: 'and ',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.w400,
                    fontFamily: TextFontEnum.poppins,
                  ),
                ),
                TextSpan(
                  text: 'Privacy Policy',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.bold,
                    fontFamily: TextFontEnum.poppins,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          pageController.launchPageOfPrivacyPolicy();
                        },
                ),
                TextSpan(
                  text: ' on data and cookies.',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.w400,
                    fontFamily: TextFontEnum.poppins,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),
        _buildSocialButtons(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BackgroundContainer(
        child: Stack(
          children: [
            // Texto original "Hey There!"
            if (_showOriginalText)
              Align(
                alignment: Alignment.center,
                child: AnimatedBuilder(
                  animation: _textFadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _textFadeAnimation.value,
                      child: Center(
                        child: Text(
                          'Hey There!',
                          style: getTextStyle(
                            45,
                            fontWeight: FontWeight.w400,
                            fontFamily: TextFontEnum.caprasimo,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            // Novo texto "I'm Lovey" (por trás da imagem)
            if (_showNewText)
              Align(
                alignment: Alignment.center,
                child: AnimatedBuilder(
                  animation: _newTextSlideAnimation,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(
                        0,
                        _newTextSlideAnimation.value * 100.h * 0.15,
                      ),
                      child: Opacity(
                        opacity: _newTextFadeAnimation.value,
                        child: Center(
                          child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              children:
                                  !_showLoveyInAppBar
                                      ? [
                                        // Texto "I'm"
                                        TextSpan(
                                          text: "I'm\n",
                                          style: getTextStyle(
                                            45,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: TextFontEnum.caprasimo,
                                            height: -0.5,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'lovey',
                                          style: getTextStyle(
                                            66.18,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: TextFontEnum.caprasimo,
                                          ),
                                        ),
                                      ]
                                      : [
                                        TextSpan(
                                          text: "Got feels? Ask me!",
                                          style: getTextStyle(
                                            25,
                                            fontWeight: FontWeight.w500,
                                            fontFamily: TextFontEnum.poppins,
                                            height: -0.5,
                                          ),
                                        ),
                                      ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            // "lovey" animado para a AppBar
            if (_showLoveyInAppBar)
              AnimatedBuilder(
                animation: _loveyToAppBarAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, _loveyToAppBarAnimation.value),
                    child: Transform.scale(
                      scale: _loveyToAppBarScaleAnimation.value,
                      child: Opacity(
                        opacity: _loveyToAppBarFadeAnimation.value,
                        child: Align(
                          alignment: Alignment.center, // Começa no centro
                          child: Text(
                            'lovey',
                            style: getTextStyle(
                              66.18, // Mesmo tamanho do original inicialmente
                              fontWeight: FontWeight.w400,
                              fontFamily: TextFontEnum.caprasimo,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            // Corpo final (_buildFinalCenterBody)
            if (_showFinalBody)
              AnimatedBuilder(
                animation: _finalBodySlideAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      0,
                      _finalBodySlideAnimation.value *
                          MediaQuery.of(context).size.height *
                          0.5,
                    ),
                    child: Opacity(
                      opacity: _finalBodyFadeAnimation.value,
                      child: Align(
                        alignment: Alignment.center,
                        child: _buildFinalCenterBody(),
                      ),
                    ),
                  );
                },
              ),
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    0,
                    (_slideAnimation.value *
                            MediaQuery.of(context).size.height) +
                        _imageDownAnimation.value,
                  ),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Transform.scale(
                      scaleY: _scaleAnimation.value,
                      alignment: Alignment.bottomCenter,
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child:
                            _isTransitioningImage
                                ? Stack(
                                  children: [
                                    // Imagem atual (fade out)
                                    Opacity(
                                      opacity:
                                          1.0 - _imageTransitionAnimation.value,
                                      child: CustomImage(_currentImage),
                                    ),
                                    // Nova imagem (fade in)
                                    Opacity(
                                      opacity: _imageTransitionAnimation.value,
                                      child: CustomImage(_nextImage),
                                    ),
                                  ],
                                )
                                : CustomImage(_currentImage),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
