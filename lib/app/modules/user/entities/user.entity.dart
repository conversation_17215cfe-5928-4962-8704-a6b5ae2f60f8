class UserEntity {
  int? id;
  String? name;
  String? googleId;
  String? appleId;
  DateTime? createdAt;

  UserEntity({this.id, this.name, this.googleId, this.appleId, this.createdAt});

  // Construtor de fábrica para criar um UserEntity a partir de um Map (ex: JSON do banco de dados)
  factory UserEntity.fromJson(Map<String, dynamic> json) {
    return UserEntity(
      id: json['id'] as int?,
      name: json['name'] as String?,
      googleId: json['google_id'] as String?,
      appleId: json['apple_id'] as String?,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
    );
  }

  // Método para converter o objeto UserEntity de volta para um Map (ex: para enviar ao banco de dados)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'google_id': googleId,
      'apple_id': appleId,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}
