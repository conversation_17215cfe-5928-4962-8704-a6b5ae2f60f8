class UserDto {
  String? name;
  String? googleId;
  String? appleId;

  UserDto({this.name = '', this.googleId, this.appleId});

  // Construtor de fábrica para criar um UserDto a partir de um Map (ex: JSON do banco de dados)
  factory UserDto.fromJson(Map<String, dynamic> json) {
    return UserDto(
      name: json['name'] as String?,
      googleId: json['google_id'] as String?,
      appleId: json['apple_id'] as String?,
    );
  }

  // Método para converter o objeto UserDto de volta para um Map (ex: para enviar ao banco de dados)
  Map<String, dynamic> toJson() {
    return {'name': name, 'google_id': googleId, 'apple_id': appleId};
  }
}
