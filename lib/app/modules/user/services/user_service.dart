import 'package:lovey_app/app/core/http_client/http_client_implementation.dart';
import 'package:lovey_app/app/modules/user/dtos/user.dto.dart';
import 'package:lovey_app/app/modules/user/entities/user.entity.dart';

class UserService {
  final HttpClientImplementation _httpClient = HttpClientImplementation();

  Future<UserEntity> createUser(UserDto user) async {
    final url = 'https://n8n.bsup.com.br/webhook/user';
    final body = user.toJson();

    final response = await _httpClient.post(url, body: body);

    if (response.statusCode != 200) {
      throw Exception('Failed to create user');
    }

    // Assumindo que a API retorna o usuário criado com o ID
    return UserEntity.fromJson(response.data);
  }
}
