class MessageDto {
  int? userId;
  String messageContent;

  MessageDto({this.userId, required this.messageContent});

  // Construtor de fábrica para criar uma MessageDto a partir de um Map
  factory MessageDto.fromJson(Map<String, dynamic> json) {
    return MessageDto(
      userId: json['user_id'] as int?,
      messageContent: json['message_content'] as String,
    );
  }

  // Método para converter o objeto MessageDto de volta para um Map
  Map<String, dynamic> toJson() {
    return {'user_id': userId, 'message_content': messageContent};
  }
}
