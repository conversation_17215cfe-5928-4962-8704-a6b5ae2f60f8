import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lovey_app/app/core/theme/colors.dart';
import 'package:lovey_app/app/core/theme/fonts.enum.dart';
import 'package:lovey_app/app/core/util/get_text_style.dart';
import 'package:lovey_app/app/widget/bouncing_dots.dart';
import 'package:sizer/sizer.dart';
import '../../core/util/assets_strings.dart';
import '../../widget/custom_image.dart';
import 'entities/message.entity.dart';
import 'services/chat_service.dart';
import 'dtos/message.dto.dart';
import '../../core/services/storage_service.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ChatService _chatService = ChatService();
  late StorageService _storageService;

  List<MessageEntity> _messages = [];
  bool _isLoading = true;
  int? _currentUserId;

  // Sistema de debounce
  Timer? _debounceTimer;
  final List<MessageDto> _pendingMessages = [];
  bool _isServerResponding = false;

  // Controladores de animação
  late AnimationController _dotsAnimationController;
  late Animation<double> _dotsOpacityAnimation;
  final List<AnimationController> _messageAnimationControllers = [];
  final List<Animation<Offset>> _messageSlideAnimations = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    // Animação para os BouncingDots (fade in/out)
    _dotsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _dotsOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _dotsAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  Future<void> _initializeApp() async {
    _storageService = await StorageService.getInstance();
    _currentUserId = _storageService.getUserId();

    if (_currentUserId != null) {
      await _loadMessages();
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadMessages() async {
    try {
      final messages = await _chatService.fetchMessages(userId: _currentUserId);

      // Cria animações para todas as mensagens carregadas com delay escalonado
      for (int i = 0; i < messages.length; i++) {
        _createMessageAnimation(
          delay: i * 100,
        ); // 100ms de delay entre cada mensagem
      }

      setState(() {
        _messages = messages;
      });
    } catch (e) {
      // Se não conseguir carregar mensagens, mantém lista vazia
      debugPrint('Erro ao carregar mensagens: $e');

      // Cria animação para a mensagem de boas-vindas
      _createMessageAnimation();

      setState(() {
        _messages = [
          MessageEntity(
            messageContent:
                "Welcome! What heart drama are we solving today? 😊",
            isFromUser: false,
            createdAt: DateTime.now().subtract(const Duration(minutes: 4)),
          ),
        ];
      });
    }
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty && _currentUserId != null) {
      final messageContent = _messageController.text.trim();

      // Adiciona mensagem temporária ao estado
      final tempMessage = MessageEntity(
        messageContent: messageContent,
        isFromUser: true,
        createdAt: DateTime.now(),
      );

      // Cria animação para a nova mensagem
      _createMessageAnimation();

      setState(() {
        _messages.add(tempMessage);
      });

      // Cria MessageDto para o debounce
      final messageDto = MessageDto(
        userId: _currentUserId!,
        messageContent: messageContent,
      );

      // Adiciona à lista de mensagens pendentes
      _pendingMessages.add(messageDto);

      // Reinicia o timer de debounce
      _startDebounceTimer();

      _messageController.clear();
    }
  }

  void _createMessageAnimation({int delay = 0}) {
    final animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    final slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0), // Começa de baixo
      end: Offset.zero, // Vai para posição normal
    ).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeOutBack),
    );

    _messageAnimationControllers.add(animationController);
    _messageSlideAnimations.add(slideAnimation);

    // Inicia a animação com delay se especificado
    if (delay > 0) {
      Future.delayed(Duration(milliseconds: delay), () {
        animationController.forward();
      });
    } else {
      animationController.forward();
    }
  }

  void _startDebounceTimer() {
    // Cancela o timer anterior se existir
    _debounceTimer?.cancel();

    // Inicia novo timer de 4 segundos
    _debounceTimer = Timer(const Duration(seconds: 4), () {
      _sendPendingMessages();
    });
  }

  Future<void> _sendPendingMessages() async {
    if (_pendingMessages.isEmpty) return;

    // Copia a lista e limpa as mensagens pendentes
    final messagesToSend = List<MessageDto>.from(_pendingMessages);
    _pendingMessages.clear();

    setState(() {
      _isServerResponding = true;
    });

    // Anima a entrada dos dots
    _dotsAnimationController.forward();

    try {
      final loveyResponse = await _chatService.sendMessages(messagesToSend);

      // Anima a saída dos dots antes de adicionar a resposta
      await _dotsAnimationController.reverse();

      // Cria animação para a resposta do bot
      _createMessageAnimation();

      setState(() {
        _messages.add(
          MessageEntity(
            messageContent: loveyResponse.messageContent,
            isFromUser: false,
            createdAt: DateTime.now(),
          ),
        );
      });
    } catch (e) {
      debugPrint('Erro ao enviar mensagens: $e');
      // Em caso de erro, anima a saída dos dots
      await _dotsAnimationController.reverse();
    } finally {
      setState(() {
        _isServerResponding = false;
      });
    }
  }

  Widget _buildPositionedMessageList() {
    return Positioned.fill(child: _buildMessagesList());
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFFFF466B), // #FF466B
                Color(0xFFFF7C97), // #FF7C97
              ],
            ),
          ),
          child: const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
        ),
      );
    }

    return Scaffold(
      // O Stack principal continua aqui, para colocar a imagem de fundo atrás de TUDO.
      body: Stack(
        fit: StackFit.expand, // Garante que os filhos preencham a tela
        children: [
          // --- CAMADAS DE FUNDO ---

          // Camada 0: O gradiente que cobre a tela inteira.
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [CustomColors.infraRed, CustomColors.tulip],
              ),
            ),
          ),

          // Camada 1: O personagem. O `Positioned` dentro dele vai funcionar
          // em relação à tela inteira, que é o que queremos.
          _buildLoveyCharacterBackground(),

          // --- CAMADA DA UI FRONTAL ---

          // Camada 2: Sua coluna de UI, que ficará por cima de tudo.
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  // AQUI ESTÁ A CORREÇÃO PRINCIPAL:
                  // Restauramos um Stack dentro do Expanded.
                  // Este Stack serve como a "área de desenho" para
                  // o Positioned da sua lista de mensagens, preservando o layout original.
                  child: Stack(children: [_buildPositionedMessageList()]),
                ),
                _buildMessageInput(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(width: 48, height: 48),
          Center(
            child: Text(
              'lovey',
              style: getTextStyle(
                32,
                fontWeight: FontWeight.w400,
                fontFamily: TextFontEnum.caprasimo,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              // Menu action
            },
            icon: const Icon(Icons.more_vert, color: Colors.white, size: 27),
          ),
        ],
      ),
    );
  }

  Widget _buildLoveyCharacterBackground() {
    final imageWidget =
        _messages.length > 1
            ? SizedBox(
              // constraints: const BoxConstraints(maxWidth: 393, maxHeight: 660),
              height: 100.h,
              width: 100.w,
              child: CustomImage(Assets.svg.loveyFill, fit: BoxFit.contain),
            )
            : Container(
              constraints: const BoxConstraints(maxWidth: 393, maxHeight: 660),
              height: 78.28.h,
              width: 100.w,
              child: CustomImage(Assets.svg.loveyMiddle, fit: BoxFit.contain),
            );
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Center(child: imageWidget),
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      reverse: true,
      itemCount: _messages.length + (_isServerResponding ? 1 : 0),
      itemBuilder: (context, index) {
        // Se é o primeiro item e o servidor está respondendo, mostra BouncingDots
        if (index == 0 && _isServerResponding) {
          return FadeTransition(
            opacity: _dotsOpacityAnimation,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.25),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: BouncingDots(
                    whenTopColor: CustomColors.tulip,
                    normalColor: CustomColors.tulip,
                  ),
                ),
              ),
            ),
          );
        }

        // Ajusta o índice para as mensagens reais
        final messageIndex = _isServerResponding ? index - 1 : index;
        final message = _messages[_messages.length - 1 - messageIndex];
        return _buildMessageBubble(message, messageIndex);
      },
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildMessageBubble(MessageEntity message, int messageIndex) {
    // Verifica se há animação disponível para esta mensagem
    final hasAnimation = messageIndex < _messageSlideAnimations.length;

    Widget messageWidget = Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment:
            message.isFromUser
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
        children: [
          Container(
            constraints: BoxConstraints(maxWidth: 85.w),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color:
                  message.isFromUser
                      ? CustomColors.barnRed.withValues(alpha: 0.28)
                      : Colors.white.withValues(
                        alpha: 0.28,
                      ), // Transparente para mensagens do bot
              borderRadius: BorderRadius.only(
                bottomLeft: const Radius.circular(22),
                bottomRight: const Radius.circular(22),
                topLeft: Radius.circular(message.isFromUser ? 22 : 0),
                topRight: Radius.circular(message.isFromUser ? 0 : 22),
              ),
            ),
            child: Text(
              message.messageContent,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          const SizedBox(height: 6),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              _formatTime(message.createdAt ?? DateTime.now()),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w300,
              ),
            ),
          ),
        ],
      ),
    );

    // Se há animação disponível, envolve com SlideTransition
    if (hasAnimation) {
      return SlideTransition(
        position: _messageSlideAnimations[messageIndex],
        child: messageWidget,
      );
    }

    return messageWidget;
  }

  Widget _buildMessageInput() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 20, 30),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.25),
        borderRadius: BorderRadius.circular(30),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'Say hello!',
                hintStyle: TextStyle(color: Colors.white70, fontSize: 16),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
              ),
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: const Icon(Icons.send, color: Colors.white, size: 24),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _dotsAnimationController.dispose();
    for (final controller in _messageAnimationControllers) {
      controller.dispose();
    }
    _messageController.dispose();
    super.dispose();
  }
}
