class MessageEntity {
  int? id;
  int? userId;
  String messageContent;
  bool isFromUser;
  DateTime? createdAt;

  MessageEntity({
    this.id,
    this.userId,
    required this.messageContent,
    required this.isFromUser,
    this.createdAt,
  });

  // Construtor de fábrica para criar uma MessageEntity a partir de um Map
  factory MessageEntity.fromJson(Map<String, dynamic> json) {
    return MessageEntity(
      id: json['id'] as int?,
      userId: json['user_id'] as int?,
      messageContent: json['message_content'] as String,
      isFromUser: json['is_from_user'] as bool,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
    );
  }

  // Método para converter o objeto MessageEntity de volta para um Map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'message_content': messageContent,
      'is_from_user': isFromUser,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}
