import 'package:lovey_app/app/core/http_client/http_client_implementation.dart';
import 'package:lovey_app/app/modules/chat/dtos/message.dto.dart';
import 'package:lovey_app/app/modules/chat/entities/message.entity.dart';

class ChatService {
  final HttpClientImplementation _httpClient = HttpClientImplementation();

  Future<void> sendMessage(MessageDto message) async {
    final url = 'https://n8n.bsup.com.br/webhook/message';
    final body = message.toJson();

    final response = await _httpClient.post(url, body: body);

    if (response.statusCode != 200) {
      throw Exception('Failed to send message');
    }
  }

  Future<MessageEntity> sendMessages(List<MessageDto> message) async {
    final url = 'https://n8n.bsup.com.br/webhook/message/bulk';
    final body = message.map((m) => m.toJson()).toList();

    final response = await _httpClient.post(url, body: body);

    if (response.statusCode != 200) {
      throw Exception('Failed to send message');
    }
    return MessageEntity.fromJson(response.data);
  }

  Future<List<MessageEntity>> fetchMessages({int? userId}) async {
    final url = 'https://n8n.bsup.com.br/webhook/message';

    Map<String, dynamic>? queryParameters;
    if (userId != null) {
      queryParameters = {'user_id': userId};
    }

    final response = await _httpClient.get(
      url,
      queryParameters: queryParameters,
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to fetch messages');
    }

    final List<dynamic> messagesJson = response.data;
    return messagesJson.map((json) => MessageEntity.fromJson(json)).toList();
  }
}
