import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/navigation/app_routes.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lovey App'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Bem-vindo ao Lovey App!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),

            // Botão para navegar ao Chat
            ElevatedButton.icon(
              onPressed: () => context.push(AppRoutes.chat),
              icon: const Icon(Icons.chat),
              label: const Text('Ir para o Chat'),
            ),

            const SizedBox(height: 16),

            // Exemplo de outros botões de navegação
            OutlinedButton.icon(
              onPressed: () {
                // Exemplo de navegação futura
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Funcionalidade em breve!')),
                );
              },
              icon: const Icon(Icons.person),
              label: const Text('Perfil (em breve)'),
            ),
          ],
        ),
      ),
    );
  }
}
