name: lovey_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

## S<PERSON><PERSON>ts
vars:
  clean: flutter clean
  get: flutter pub get
  runner: flutter pub run build_runner
scripts:
  mobx_build: $runner build
  mobx_watch: $clean & $get & $runner watch
  mobx_build_clean: $clean & $get & $runner build --delete-conflicting-outputs
  build: flutter pub run build_runner build
  clean: flutter pub run build_runner clean
  buildRelease: flutter build apk --release
  lint: flutter format ./lib
  gen_icons: flutter pub run flutter_launcher_icons -f flutter_launcher_icons.yaml
# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  
  # Navegação moderna e robusta
  go_router: ^14.6.2
  flutter_launcher_icons: ^0.14.4
  flutter_svg: ^2.2.1
  skeletonizer: ^1.4.3
  sizer: ^3.1.3
  flutter_dotenv: ^6.0.0
  dio: ^5.9.0
  shared_preferences: ^2.3.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Seção de fontes
  fonts:
    # Família Albert Sans (Variável)
    - family: AlbertSans
      fonts:
        - asset: assets/fonts/Albert_Sans/AlbertSans-VariableFont_wght.ttf

    # Família Caprasimo (Estática)
    - family: Caprasimo
      fonts:
        - asset: assets/fonts/Caprasimo/Caprasimo-Regular.ttf
    # Família Outfit (Variável)
    - family: Outfit
      fonts:
        - asset: assets/fonts/Outfit/Outfit-VariableFont_wght.ttf
    # Família Poppins (Estática)
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
        
  # To add assets to your application, add an assets section, like this:
  assets:
    # Imagens SVG
    - assets/img/svg/
    # Imagens PNG
    - assets/img/png/
    # Áudios MP3
    - assets/audio/mp3/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
