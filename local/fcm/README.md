# 🔔 Scripts de Teste FCM - Quycky App

## 📁 Arquivos Organizados

Todos os scripts para teste de notificações push estão nesta pasta `fcm/`.

## 🚀 Uso Rápido

### 1. **<PERSON><PERSON><PERSON><PERSON> Recomendado (FCM v1 com gcloud)**:

```bash
# Instalar gcloud CLI
brew install google-cloud-sdk

# Autenticar
gcloud auth application-default login

# Obter access token e enviar notificação
ACCESS_TOKEN=$(gcloud auth application-default print-access-token)
./send_fcm_manual.sh "SEU_FCM_TOKEN" "$ACCESS_TOKEN"
```

### 2. **Diagnóstico (para verificar APIs disponíveis)**:

```bash
./test_fcm_simple.sh "SEU_FCM_TOKEN"
```

### 3. **Configuração Completa**:

```bash
./setup_fcm_v1.sh
```

## 📋 Lista de Scripts

| Script | Descrição | Uso |
|--------|-----------|-----|
| `setup_fcm_v1.sh` | Guia de configuração e cria scripts auxiliares | `./setup_fcm_v1.sh` |
| `test_fcm_simple.sh` | Testa diferentes APIs FCM | `./test_fcm_simple.sh "TOKEN"` |
| `send_fcm_manual.sh` | Envia via FCM v1 com access token | `./send_fcm_manual.sh "TOKEN" "ACCESS_TOKEN"` |
| `send_fcm_v1_simple.sh` | FCM v1 com Service Account JSON | `./send_fcm_v1_simple.sh "TOKEN"` |
| `send_notification_v1.sh` | FCM v1 com gcloud integrado | `./send_notification_v1.sh "TOKEN"` |
| `debug_notification.sh` | Debug detalhado de requisições | `./debug_notification.sh "TOKEN"` |
| `quick_test.sh` | Teste rápido (Legacy - não funciona) | `./quick_test.sh "TOKEN"` |
| `send_notification.sh` | Script completo (Legacy - não funciona) | `./send_notification.sh --help` |
| `send_gcm.sh` | Alternativa GCM (não funciona) | `./send_gcm.sh "TOKEN"` |

## ⚠️ Status das APIs

- ❌ **FCM Legacy API**: Descontinuada (404)
- ❌ **GCM API**: Não disponível (404)
- ✅ **FCM v1 API**: Funcionando (requer autenticação OAuth2)

## 🎯 Próximos Passos

1. **Execute o diagnóstico** para confirmar o status:
   ```bash
   cd fcm
   ./test_fcm_simple.sh "SEU_FCM_TOKEN_COMPLETO"
   ```

2. **Configure FCM v1** seguindo o guia:
   ```bash
   ./setup_fcm_v1.sh
   ```

3. **Teste a notificação** com o método recomendado

## 📖 Documentação Completa

Veja `NOTIFICATION_TEST_README.md` para instruções detalhadas.

## 🔧 Troubleshooting

- **Token FCM**: Deve ter ~142-163 caracteres
- **Dispositivo**: Use dispositivo físico (não simulador)
- **Background App Refresh**: Deve estar habilitado no iOS
- **Permissões**: App deve ter permissão para notificações e badges
