#!/bin/bash

# =============================================================================
# Script de Debug para Notificações FCM
# =============================================================================

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Debug FCM Notification ===${NC}"
echo ""

if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN${NC}"
    exit 1
fi

FCM_TOKEN="$1"
SERVER_KEY="AAAAYFEF1wk:APA91bE-MwrtELAWm2xkZN1wwzOLfPyloumsTq1oOFGHB8lUDqmW07XG8KMwAp36rtBhGqdy5_rSqZrHnlqslHnBuWzmYt1plkuvntQe1xGTjqEow9vkroSoHOKamHvXcSjA88P8Wg5v"

echo -e "${BLUE}🔍 Informações de Debug:${NC}"
echo "Token length: ${#FCM_TOKEN}"
echo "Token preview: ${FCM_TOKEN:0:50}..."
echo "Server Key length: ${#SERVER_KEY}"
echo "Server Key preview: ${SERVER_KEY:0:20}..."
echo ""

# Payload simples para teste
payload='{
    "to": "'$FCM_TOKEN'",
    "data": {
        "test": "debug"
    }
}'

echo -e "${YELLOW}📤 Enviando requisição de debug...${NC}"
echo "URL: https://fcm.googleapis.com/fcm/send"
echo "Payload: $payload"
echo ""

# Fazer requisição com verbose
echo -e "${BLUE}🌐 Resposta completa:${NC}"
curl -v -X POST \
    -H "Authorization: key=$SERVER_KEY" \
    -H "Content-Type: application/json" \
    -d "$payload" \
    "https://fcm.googleapis.com/fcm/send"

echo ""
echo ""
echo -e "${YELLOW}💡 Verificações:${NC}"
echo "1. Token FCM está correto e não expirado?"
echo "2. Server Key está correta?"
echo "3. Projeto Firebase está ativo?"
echo "4. App está registrado no projeto?"
