#!/bin/bash

# =============================================================================
# Script Simples para Testar FCM usando diferentes métodos
# =============================================================================

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Teste FCM - Múltiplos Métodos ===${NC}"
echo ""

if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN${NC}"
    exit 1
fi

FCM_TOKEN="$1"
SERVER_KEY="AAAAYFEF1wk:APA91bE-MwrtELAWm2xkZN1wwzOLfPyloumsTq1oOFGHB8lUDqmW07XG8KMwAp36rtBhGqdy5_rSqZrHnlqslHnBuWzmYt1plkuvntQe1xGTjqEow9vkroSoHOKamHvXcSjA88P8Wg5v"
PROJECT_ID="quycky-22841"

echo -e "${YELLOW}🔍 Testando diferentes endpoints FCM...${NC}"
echo ""

# Teste 1: Legacy API (que já sabemos que dá 404)
echo -e "${BLUE}1️⃣ Testando Legacy API...${NC}"
response1=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
    -H "Authorization: key=$SERVER_KEY" \
    -H "Content-Type: application/json" \
    -d '{"to":"'$FCM_TOKEN'","data":{"test":"legacy"}}' \
    "https://fcm.googleapis.com/fcm/send")
echo "Status: $response1"

# Teste 2: Tentar com endpoint alternativo
echo -e "${BLUE}2️⃣ Testando endpoint alternativo...${NC}"
response2=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
    -H "Authorization: key=$SERVER_KEY" \
    -H "Content-Type: application/json" \
    -d '{"to":"'$FCM_TOKEN'","data":{"test":"alt"}}' \
    "https://android.googleapis.com/gcm/send")
echo "Status: $response2"

# Teste 3: Verificar se o projeto aceita v1 sem auth (vai dar 401, mas confirma que existe)
echo -e "${BLUE}3️⃣ Testando FCM v1 endpoint...${NC}"
response3=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d '{"message":{"token":"'$FCM_TOKEN'","data":{"test":"v1"}}}' \
    "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send")
echo "Status: $response3"

echo ""
echo -e "${YELLOW}📊 Análise dos resultados:${NC}"
if [[ "$response1" == "404" ]]; then
    echo "❌ Legacy API: Descontinuada ou não disponível"
else
    echo "✅ Legacy API: Disponível (status: $response1)"
fi

if [[ "$response2" == "404" ]]; then
    echo "❌ GCM API: Não disponível"
else
    echo "✅ GCM API: Disponível (status: $response2)"
fi

if [[ "$response3" == "401" ]]; then
    echo "✅ FCM v1 API: Disponível (precisa de autenticação)"
elif [[ "$response3" == "404" ]]; then
    echo "❌ FCM v1 API: Projeto não encontrado"
else
    echo "🤔 FCM v1 API: Status inesperado ($response3)"
fi

echo ""
echo -e "${BLUE}💡 Próximos passos:${NC}"
if [[ "$response3" == "401" ]]; then
    echo "✅ Use a FCM v1 API com autenticação OAuth2"
    echo "📋 Siga as instruções abaixo para configurar:"
    echo ""
    echo "1. Vá para Firebase Console > Project Settings"
    echo "2. Aba 'Service Accounts'"
    echo "3. Clique em 'Generate new private key'"
    echo "4. Baixe o arquivo JSON"
    echo "5. Execute: export GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json"
    echo "6. Use o script: ./send_notification_v1.sh"
elif [[ "$response2" != "404" ]]; then
    echo "✅ Tente usar a GCM API como alternativa"
    echo "📋 Vou criar um script para GCM..."
else
    echo "❌ Todas as APIs estão inacessíveis"
    echo "📋 Verifique se o projeto Firebase está ativo"
fi
