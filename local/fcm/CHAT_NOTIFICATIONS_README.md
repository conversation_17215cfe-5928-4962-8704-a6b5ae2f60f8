# Chat Notifications - Sistema de Notificações para Mensagens

## Funcionalidade Implementada

Foi implementado um sistema de notificações locais que mostra notificações do sistema quando chegam novas mensagens via socket e o app está minimizado/em background.

## Arquivos Modificados/Criados

### 1. `lib/core/services/app_lifecycle_service/app_lifecycle_service.dart` (NOVO)
- Serviço singleton para monitorar o estado do ciclo de vida do app
- Detecta quando o app está em foreground ou background
- Usa `WidgetsBindingObserver` para escutar mudanças de estado

### 2. `lib/app/features/friendship/presenter/services/chat_socket_handler.dart` (MODIFICADO)
- Adicionada lógica para mostrar notificações quando o app está em background
- Novo método `_showNotificationIfAppInBackground()` que:
  - Verifica se o app está em background
  - Cria notificação com nome do remetente e conteúdo da mensagem
  - Usa `LocalNotificationService.showNotification()` para mostrar a notificação

### 3. `lib/main.dart` (MODIFICADO)
- Inicialização do `AppLifecycleService`
- Inicialização do `LocalNotificationService`

### 4. `lib/app/modules/app_module.dart` (MODIFICADO)
- Adicionado bind do `AppLifecycleService` como singleton

## Como Funciona

1. **Detecção de Estado**: O `AppLifecycleService` monitora continuamente o estado do app
2. **Recebimento de Mensagem**: Quando uma nova mensagem chega via socket
3. **Verificação de Estado**: O sistema verifica se o app está em background
4. **Criação de Notificação**: Se estiver em background, cria uma notificação local com:
   - Título: Nome do remetente (ou identifier se nome não disponível)
   - Corpo: Conteúdo da mensagem
   - Payload: Dados da mensagem em JSON para ações futuras

## Como Testar

### Teste Manual Detalhado

#### 1. **Preparação**:
   - Compile e instale o app no dispositivo: `flutter run`
   - Faça login com dois usuários diferentes (um em cada dispositivo)
   - Na tela inicial (HomePage), você verá dois botões flutuantes no canto inferior direito:
     - 🔔 (laranja): Testa notificação diretamente
     - ℹ️ (azul): Mostra status do sistema

#### 2. **Teste Básico de Notificação**:
   - Toque no botão 🔔 (laranja) para testar se as notificações estão funcionando
   - Deve aparecer uma notificação de teste no sistema
   - Verifique os logs no console para ver se há erros

#### 3. **Teste de Estado do App**:
   - Toque no botão ℹ️ (azul) para ver o status atual:
     - Estado do app (resumed, paused, etc.)
     - Se está em background ou foreground
     - Se o serviço de notificações está inicializado
   - Minimize o app e toque novamente para ver a mudança de estado

#### 4. **Teste de Notificação de Chat**:
   - **Dispositivo A**: Abra o app e vá para a tela de chat
   - **Dispositivo B**: Minimize o app (coloque em background)
   - **Dispositivo A**: Envie uma mensagem para o usuário do dispositivo B
   - **Dispositivo B**: Deve aparecer uma notificação do sistema

#### 5. **Verificação dos Logs**:
   Monitore os logs no console para ver:
   ```
   🔔 LocalNotificationService initialized: true
   🔔 Android/iOS notification permission granted: true
   🔔 LocalNotificationService initialization completed
   ChatSocketHandler: App lifecycle state: AppLifecycleState.paused
   ChatSocketHandler: Is in background: true
   ChatSocketHandler: App está em background, preparando notificação...
   🔔 Attempting to show notification: [título] - [mensagem]
   🔔 Notification shown successfully
   ```

#### 6. **Verificação da Notificação**:
   - A notificação deve mostrar o nome/identifier do remetente
   - O conteúdo da mensagem deve aparecer na notificação
   - Tocar na notificação deve abrir o app

#### 7. **Troubleshooting**:
   Se as notificações não aparecerem:
   - Verifique se as permissões foram concedidas
   - Verifique os logs para erros
   - Teste primeiro com o botão 🔔 para ver se o sistema básico funciona
   - Verifique se o app realmente está em background (use o botão ℹ️)

### Logs de Debug
O sistema gera logs para debug:
```
ChatSocketHandler: App em foreground, não mostrando notificação
ChatSocketHandler: Notificação mostrada para mensagem de [nome_remetente]
ChatSocketHandler: Erro ao mostrar notificação: [erro]
AppLifecycleService: Estado mudou de [estado_anterior] para [novo_estado]
```

## Estados do App Considerados como Background
- `AppLifecycleState.paused`: App pausado (minimizado)
- `AppLifecycleState.hidden`: App oculto
- `AppLifecycleState.detached`: App desanexado

## Payload da Notificação
```json
{
  "type": "CHAT_MESSAGE",
  "title": "Nome do Remetente",
  "message": "Conteúdo da mensagem",
  "data": {
    "sender_identifier": "identifier_do_remetente",
    "receiver_identifier": "identifier_do_destinatario", 
    "message_id": 123,
    "created_at": "2023-01-01T12:00:00.000Z"
  }
}
```

## Considerações Técnicas

### Permissões
- As permissões de notificação são gerenciadas pelo `LocalNotificationService`
- No iOS, as permissões são solicitadas automaticamente na primeira notificação
- No Android, as notificações são mostradas por padrão

### Performance
- O `AppLifecycleService` é um singleton leve que apenas escuta mudanças de estado
- As notificações só são criadas quando necessário (app em background + nova mensagem)
- Não há impacto na performance quando o app está em foreground

### Limitações
- As notificações só aparecem quando o app está em background
- Se o app estiver completamente fechado, as notificações dependem do Firebase Cloud Messaging
- O nome do remetente pode não estar disponível em todas as mensagens (usa identifier como fallback)

## Possíveis Problemas e Soluções

### 1. **Notificações não aparecem**
- **Causa**: Permissões não concedidas
- **Solução**: Verifique os logs para ver se as permissões foram concedidas. No Android 13+, pode ser necessário conceder permissões manualmente nas configurações do app.

### 2. **App não detecta estado de background**
- **Causa**: AppLifecycleService não inicializado corretamente
- **Solução**: Verifique se `AppLifecycleService().initialize()` está sendo chamado no main.dart

### 3. **LocalNotificationService não inicializado**
- **Causa**: Serviço não foi inicializado no main.dart
- **Solução**: Verifique se `await LocalNotificationService.initialize()` está sendo chamado no main.dart

### 4. **Notificações aparecem mesmo com app em foreground**
- **Causa**: Lógica de detecção de estado incorreta
- **Solução**: Use o botão ℹ️ para verificar o estado atual do app

### 5. **Erro "LocalNotificationService not initialized"**
- **Causa**: Tentativa de mostrar notificação antes da inicialização
- **Solução**: Aguarde a inicialização completa do app antes de enviar mensagens

## Próximos Passos Possíveis

1. **Melhorar Dados do Remetente**: Buscar informações do usuário remetente para mostrar nome completo
2. **Ações na Notificação**: Adicionar ações como "Responder" ou "Marcar como Lida"
3. **Agrupamento**: Agrupar múltiplas mensagens do mesmo remetente
4. **Personalização**: Permitir que o usuário configure tipos de notificação
5. **Badge Count**: Atualizar o badge do ícone do app com número de mensagens não lidas
6. **Remover Widgets de Debug**: Remover os botões de teste da HomePage após confirmar que tudo funciona
