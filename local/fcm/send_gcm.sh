#!/bin/bash

# =============================================================================
# Script para Envio via GCM API (alternativa ao FCM Legacy)
# =============================================================================

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== GCM API Notification Sender ===${NC}"
echo ""

if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN [BADGE_COUNT]${NC}"
    exit 1
fi

FCM_TOKEN="$1"
BADGE_COUNT="${2:-1}"
SERVER_KEY="AAAAYFEF1wk:APA91bE-MwrtELAWm2xkZN1wwzOLfPyloumsTq1oOFGHB8lUDqmW07XG8KMwAp36rtBhGqdy5_rSqZrHnlqslHnBuWzmYt1plkuvntQe1xGTjqEow9vkroSoHOKamHvXcSjA88P8Wg5v"

echo -e "${YELLOW}📱 Enviando via GCM API...${NC}"
echo "Token: ${FCM_TOKEN:0:30}..."
echo "Badge: $BADGE_COUNT"
echo ""

# Payload para GCM (formato similar ao FCM legacy)
payload='{
    "to": "'$FCM_TOKEN'",
    "content_available": true,
    "priority": "high",
    "data": {
        "type": "INVITE_GAME_ROOM_PUSH_MESSAGE",
        "user": {
            "name": "Test User"
        },
        "message": "Teste via GCM API",
        "timestamp": "'$(date +%s)'"
    }
}'

# Enviar via GCM
response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Authorization: key=$SERVER_KEY" \
    -H "Content-Type: application/json" \
    -d "$payload" \
    "https://android.googleapis.com/gcm/send")

http_code=$(echo "$response" | tail -n 1)
response_body=$(echo "$response" | sed '$d')

if [[ "$http_code" == "200" ]]; then
    echo -e "${GREEN}✅ Notificação enviada via GCM!${NC}"
    echo -e "${GREEN}Resposta: ${NC}$response_body"
    
    # Verificar se houve sucesso
    if echo "$response_body" | grep -q '"success":1'; then
        echo -e "${GREEN}🎉 Notificação entregue com sucesso!${NC}"
    elif echo "$response_body" | grep -q '"failure":1'; then
        echo -e "${YELLOW}⚠️  Falha na entrega da notificação${NC}"
        if echo "$response_body" | grep -q '"error"'; then
            error=$(echo "$response_body" | grep -o '"error":"[^"]*"' | cut -d'"' -f4)
            echo -e "${RED}Erro: ${NC}$error"
        fi
    fi
else
    echo -e "${RED}❌ Erro: HTTP $http_code${NC}"
    echo -e "${RED}Resposta: ${NC}$response_body"
fi

echo ""
echo -e "${BLUE}🔍 Verificações:${NC}"
echo "1. Verifique se o badge apareceu no ícone do app"
echo "2. Monitore os logs do Flutter para ver se o background handler executou"
echo "3. Se não funcionou, tente a FCM v1 API com autenticação OAuth2"
