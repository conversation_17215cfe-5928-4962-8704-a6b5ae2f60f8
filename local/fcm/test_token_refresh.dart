// Script de teste para regenerar token FCM
// Execute este código no app para forçar regeneração do token

import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/core/services/push_service/abstract_push_service.dart';

class FCMTokenTester {
  static Future<void> testTokenRefresh() async {
    try {
      print('🔔 === TESTE DE REGENERAÇÃO DE TOKEN FCM ===');

      final pushService = Modular.get<AbstractPushService>();

      // 1. Mostra token atual
      String currentToken = await pushService.getToken();
      print('🔔 Token atual: ${currentToken.substring(0, 30)}...');

      // 2. Força regeneração
      print('🔔 Forçando regeneração do token...');
      String newToken = await pushService.forceTokenRefresh();

      if (newToken.isNotEmpty) {
        print('🔔 ✅ Novo token gerado: ${newToken.substring(0, 30)}...');
        print('🔔 ✅ Token foi atualizado no servidor automaticamente');

        // 3. Verifica se mudou
        if (currentToken != newToken) {
          print('🔔 ✅ Token foi alterado com sucesso');
        } else {
          print('🔔 ⚠️ Token permaneceu o mesmo');
        }
      } else {
        print('🔔 ❌ Falha ao gerar novo token');
      }

      print('🔔 === FIM DO TESTE ===');
    } catch (e) {
      print('🔔 ❌ Erro no teste: $e');
    }
  }
}

// Para usar no app, adicione este código em algum botão de debug:
/*
ElevatedButton(
  onPressed: () => FCMTokenTester.testTokenRefresh(),
  child: Text('Regenerar Token FCM'),
)
*/
