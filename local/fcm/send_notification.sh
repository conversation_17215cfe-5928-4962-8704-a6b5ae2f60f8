#!/bin/bash

# =============================================================================
# Script para Envio de Notificações Push FCM - Quycky App
# =============================================================================

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações
FCM_URL="https://fcm.googleapis.com/fcm/send"

# IMPORTANTE: Substitua pela sua Server Key do Firebase
# Encontre em: Firebase Console > Project Settings > Cloud Messaging > Server Key
SERVER_KEY="AAAAYFEF1wk:APA91bE-MwrtELAWm2xkZN1wwzOLfPyloumsTq1oOFGHB8lUDqmW07XG8KMwAp36rtBhGqdy5_rSqZrHnlqslHnBuWzmYt1plkuvntQe1xGTjqEow9vkroSoHOKamHvXcSjA88P8Wg5v"

# Função para mostrar ajuda
show_help() {
    echo -e "${BLUE}=== Script de Envio de Notificações Push FCM ===${NC}"
    echo ""
    echo "Uso: $0 [OPÇÕES] FCM_TOKEN"
    echo ""
    echo "Opções:"
    echo "  -t, --token TOKEN        Token FCM do dispositivo (obrigatório)"
    echo "  -k, --key SERVER_KEY     Server Key do Firebase"
    echo "  -m, --message MESSAGE    Mensagem personalizada"
    echo "  -u, --user USER_NAME     Nome do usuário que está enviando"
    echo "  --type TYPE              Tipo da notificação (padrão: INVITE_GAME_ROOM_PUSH_MESSAGE)"
    echo "  --badge NUMBER           Número do badge (padrão: 1)"
    echo "  --silent                 Enviar como silent push (sem notificação visual)"
    echo "  --test                   Enviar notificação de teste simples"
    echo "  -h, --help               Mostrar esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0 -t \"fcm_token_aqui\" --test"
    echo "  $0 -t \"fcm_token_aqui\" -u \"João\" -m \"Te desafio para um jogo!\""
    echo "  $0 -t \"fcm_token_aqui\" --silent --badge 5"
}

# Valores padrão
FCM_TOKEN=""
MESSAGE="Te desafio para um jogo!"
USER_NAME="Test User"
NOTIFICATION_TYPE="INVITE_GAME_ROOM_PUSH_MESSAGE"
BADGE_COUNT=1
SILENT_MODE=false
TEST_MODE=false

# Parse dos argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--token)
            FCM_TOKEN="$2"
            shift 2
            ;;
        -k|--key)
            SERVER_KEY="$2"
            shift 2
            ;;
        -m|--message)
            MESSAGE="$2"
            shift 2
            ;;
        -u|--user)
            USER_NAME="$2"
            shift 2
            ;;
        --type)
            NOTIFICATION_TYPE="$2"
            shift 2
            ;;
        --badge)
            BADGE_COUNT="$2"
            shift 2
            ;;
        --silent)
            SILENT_MODE=true
            shift
            ;;
        --test)
            TEST_MODE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            if [[ -z "$FCM_TOKEN" ]]; then
                FCM_TOKEN="$1"
            fi
            shift
            ;;
    esac
done

# Validações
if [[ -z "$FCM_TOKEN" ]]; then
    echo -e "${RED}❌ Erro: Token FCM é obrigatório${NC}"
    echo "Use: $0 --help para ver as opções"
    exit 1
fi

if [[ "$SERVER_KEY" == "YOUR_FIREBASE_SERVER_KEY_HERE" ]]; then
    echo -e "${RED}❌ Erro: Configure sua Server Key do Firebase${NC}"
    echo "Edite o script e substitua YOUR_FIREBASE_SERVER_KEY_HERE pela sua chave"
    echo "Encontre em: Firebase Console > Project Settings > Cloud Messaging > Server Key"
    exit 1
fi

# Função para enviar notificação de teste simples
send_test_notification() {
    echo -e "${YELLOW}📱 Enviando notificação de teste...${NC}"
    
    local payload='{
        "to": "'$FCM_TOKEN'",
        "content_available": true,
        "priority": "high",
        "data": {
            "type": "TEST_NOTIFICATION",
            "message": "Teste de notificação em background",
            "timestamp": "'$(date +%s)'"
        },
        "apns": {
            "payload": {
                "aps": {
                    "content-available": 1,
                    "badge": '$BADGE_COUNT'
                }
            }
        }
    }'
    
    send_notification "$payload"
}

# Função para enviar notificação de convite para jogo
send_game_invite() {
    echo -e "${YELLOW}🎮 Enviando convite para jogo...${NC}"
    
    local payload
    if [[ "$SILENT_MODE" == true ]]; then
        # Silent push - apenas executa código em background
        payload='{
            "to": "'$FCM_TOKEN'",
            "content_available": true,
            "priority": "high",
            "data": {
                "type": "'$NOTIFICATION_TYPE'",
                "user": {
                    "name": "'$USER_NAME'"
                },
                "message": "'$MESSAGE'",
                "timestamp": "'$(date +%s)'"
            },
            "apns": {
                "payload": {
                    "aps": {
                        "content-available": 1,
                        "badge": '$BADGE_COUNT'
                    }
                }
            }
        }'
    else
        # Notificação com alerta visual
        payload='{
            "to": "'$FCM_TOKEN'",
            "content_available": true,
            "priority": "high",
            "notification": {
                "title": "'$USER_NAME'",
                "body": "'$MESSAGE'"
            },
            "data": {
                "type": "'$NOTIFICATION_TYPE'",
                "user": {
                    "name": "'$USER_NAME'"
                },
                "message": "'$MESSAGE'",
                "timestamp": "'$(date +%s)'"
            },
            "apns": {
                "payload": {
                    "aps": {
                        "content-available": 1,
                        "badge": '$BADGE_COUNT',
                        "alert": {
                            "title": "'$USER_NAME'",
                            "body": "'$MESSAGE'"
                        }
                    }
                }
            }
        }'
    fi
    
    send_notification "$payload"
}

# Função para enviar a notificação
send_notification() {
    local payload="$1"
    
    echo -e "${BLUE}📤 Enviando para FCM...${NC}"
    echo -e "${BLUE}Token: ${NC}${FCM_TOKEN:0:20}..."
    echo -e "${BLUE}Tipo: ${NC}$NOTIFICATION_TYPE"
    echo -e "${BLUE}Badge: ${NC}$BADGE_COUNT"
    echo -e "${BLUE}Silent: ${NC}$SILENT_MODE"
    echo ""
    
    # Enviar requisição
    response=$(curl -s -w "\n%{http_code}" -X POST \
        -H "Authorization: key=$SERVER_KEY" \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "$FCM_URL")
    
    # Separar resposta e código HTTP
    http_code=$(echo "$response" | tail -n 1)
    response_body=$(echo "$response" | sed '$d')
    
    # Verificar resultado
    if [[ "$http_code" == "200" ]]; then
        echo -e "${GREEN}✅ Notificação enviada com sucesso!${NC}"
        echo -e "${GREEN}Resposta: ${NC}$response_body"
        
        # Verificar se houve falha no envio
        if echo "$response_body" | grep -q '"failure":0'; then
            echo -e "${GREEN}🎉 Notificação entregue ao FCM${NC}"
        else
            echo -e "${YELLOW}⚠️  Possível falha na entrega${NC}"
        fi
    else
        echo -e "${RED}❌ Erro no envio da notificação${NC}"
        echo -e "${RED}Código HTTP: ${NC}$http_code"
        echo -e "${RED}Resposta: ${NC}$response_body"
        exit 1
    fi
}

# Executar baseado no modo
echo -e "${BLUE}=== Quycky Push Notification Sender ===${NC}"
echo ""

if [[ "$TEST_MODE" == true ]]; then
    send_test_notification
else
    send_game_invite
fi

echo ""
echo -e "${BLUE}📱 Verifique o dispositivo para ver se a notificação chegou${NC}"
echo -e "${BLUE}🔍 Monitore os logs do Flutter para ver se o background handler foi executado${NC}"
