#!/bin/bash

# =============================================================================
# Script Rápido para Teste de Notificações - <PERSON><PERSON><PERSON> App
# =============================================================================

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}=== Teste Rápido de Notificação Push ===${NC}"
echo ""

# Verificar se o token foi fornecido
if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN${NC}"
    echo ""
    echo "Exemplo:"
    echo "  $0 \"dA1B2c3D4e5F6g7H8i9J0k1L2m3N4o5P6q7R8s9T0u1V2w3X4y5Z6\""
    echo ""
    echo -e "${YELLOW}💡 Para obter o FCM Token:${NC}"
    echo "1. Execute o app no dispositivo"
    echo "2. Procure nos logs por: '🔔 FCM Token: ...'"
    echo "3. Copie o token completo"
    exit 1
fi

FCM_TOKEN="$1"

# IMPORTANTE: Configure sua Server Key aqui
SERVER_KEY="AAAAYFEF1wk:APA91bE-MwrtELAWm2xkZN1wwzOLfPyloumsTq1oOFGHB8lUDqmW07XG8KMwAp36rtBhGqdy5_rSqZrHnlqslHnBuWzmYt1plkuvntQe1xGTjqEow9vkroSoHOKamHvXcSjA88P8Wg5v"

if [[ "$SERVER_KEY" == "YOUR_FIREBASE_SERVER_KEY_HERE" ]]; then
    echo -e "${RED}❌ Configure sua Server Key do Firebase${NC}"
    echo ""
    echo "1. Vá para Firebase Console"
    echo "2. Project Settings > Cloud Messaging"
    echo "3. Copie a 'Server Key'"
    echo "4. Edite este script e substitua YOUR_FIREBASE_SERVER_KEY_HERE"
    exit 1
fi

echo -e "${YELLOW}📱 Enviando notificação silent push para teste de badge...${NC}"
echo -e "${BLUE}Token: ${NC}${FCM_TOKEN:0:30}..."
echo ""

# Payload para silent push (executa código em background)
payload='{
    "to": "'$FCM_TOKEN'",
    "content_available": true,
    "priority": "high",
    "data": {
        "type": "INVITE_GAME_ROOM_PUSH_MESSAGE",
        "user": {
            "name": "Test User"
        },
        "message": "Teste de notificação em background",
        "timestamp": "'$(date +%s)'"
    },
    "apns": {
        "payload": {
            "aps": {
                "content-available": 1,
                "badge": 1
            }
        }
    }
}'

# Enviar
response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Authorization: key=$SERVER_KEY" \
    -H "Content-Type: application/json" \
    -d "$payload" \
    "https://fcm.googleapis.com/fcm/send")

http_code=$(echo "$response" | tail -n 1)
response_body=$(echo "$response" | sed '$d')

if [[ "$http_code" == "200" ]]; then
    echo -e "${GREEN}✅ Notificação enviada!${NC}"
    echo -e "${GREEN}Resposta: ${NC}$response_body"
    echo ""
    echo -e "${YELLOW}🔍 O que verificar:${NC}"
    echo "1. O badge deve aparecer no ícone do app (se o app estiver fechado)"
    echo "2. Nos logs do Flutter, procure por:"
    echo "   - '🔔 [BACKGROUND] Message received:'"
    echo "   - '🔔 Badge updated successfully'"
    echo ""
    echo -e "${BLUE}💡 Dicas:${NC}"
    echo "- Certifique-se de que o app está completamente fechado"
    echo "- Verifique se 'Background App Refresh' está habilitado"
    echo "- Use um dispositivo físico (não funciona no simulador)"
else
    echo -e "${RED}❌ Erro: HTTP $http_code${NC}"
    echo -e "${RED}Resposta: ${NC}$response_body"
fi
