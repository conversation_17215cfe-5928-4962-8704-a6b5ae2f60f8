#!/bin/bash

# =============================================================================
# Script para Envio de Notificações FCM usando API v1
# =============================================================================

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== FCM v1 API Notification Sender ===${NC}"
echo ""

if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN [BADGE_COUNT]${NC}"
    echo ""
    echo "Para usar a API v1, você precisa:"
    echo "1. Gerar um Service Account Key JSON"
    echo "2. Instalar gcloud CLI ou usar OAuth2"
    echo ""
    echo "Alternativamente, use o script legacy:"
    echo "./quick_test.sh FCM_TOKEN"
    exit 1
fi

FCM_TOKEN="$1"
BADGE_COUNT="${2:-1}"
PROJECT_ID="quycky-22841"

echo -e "${YELLOW}⚠️  Este script requer autenticação OAuth2 ou Service Account${NC}"
echo -e "${YELLOW}Para teste rápido, use: ./debug_notification.sh${NC}"
echo ""

# Verificar se gcloud está instalado
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI não encontrado${NC}"
    echo ""
    echo "Para instalar:"
    echo "1. Vá para: https://cloud.google.com/sdk/docs/install"
    echo "2. Ou use: brew install google-cloud-sdk"
    echo ""
    echo "Alternativamente, use o script legacy:"
    echo "./debug_notification.sh $FCM_TOKEN"
    exit 1
fi

# Obter access token
echo -e "${BLUE}🔑 Obtendo access token...${NC}"
ACCESS_TOKEN=$(gcloud auth application-default print-access-token 2>/dev/null)

if [[ -z "$ACCESS_TOKEN" ]]; then
    echo -e "${RED}❌ Erro ao obter access token${NC}"
    echo ""
    echo "Configure a autenticação:"
    echo "gcloud auth application-default login"
    echo ""
    echo "Ou use o script legacy:"
    echo "./debug_notification.sh $FCM_TOKEN"
    exit 1
fi

# Payload para FCM v1
payload='{
    "message": {
        "token": "'$FCM_TOKEN'",
        "data": {
            "type": "INVITE_GAME_ROOM_PUSH_MESSAGE",
            "user_name": "Test User",
            "message": "Teste de notificação v1"
        },
        "apns": {
            "payload": {
                "aps": {
                    "content-available": 1,
                    "badge": '$BADGE_COUNT'
                }
            }
        }
    }
}'

echo -e "${YELLOW}📱 Enviando notificação v1...${NC}"
echo "Token: ${FCM_TOKEN:0:30}..."
echo "Badge: $BADGE_COUNT"
echo ""

# Enviar usando FCM v1 API
response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d "$payload" \
    "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send")

http_code=$(echo "$response" | tail -n 1)
response_body=$(echo "$response" | sed '$d')

if [[ "$http_code" == "200" ]]; then
    echo -e "${GREEN}✅ Notificação enviada com sucesso!${NC}"
    echo -e "${GREEN}Resposta: ${NC}$response_body"
else
    echo -e "${RED}❌ Erro: HTTP $http_code${NC}"
    echo -e "${RED}Resposta: ${NC}$response_body"
    echo ""
    echo -e "${YELLOW}💡 Tente o script legacy:${NC}"
    echo "./debug_notification.sh $FCM_TOKEN"
fi
