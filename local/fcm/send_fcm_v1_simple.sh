#!/bin/bash

# =============================================================================
# Script FCM v1 com Service Account Key (sem gcloud CLI)
# =============================================================================

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== FCM v1 API - Service Account Method ===${NC}"
echo ""

if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN [SERVICE_ACCOUNT_JSON_PATH]${NC}"
    echo ""
    echo "Exemplo:"
    echo "  $0 \"seu_fcm_token\" \"/path/to/service-account.json\""
    echo ""
    echo -e "${YELLOW}📋 Para obter o Service Account JSON:${NC}"
    echo "1. Firebase Console > Project Settings > Service Accounts"
    echo "2. 'Generate new private key' > Download JSON"
    echo "3. Salve como 'firebase-service-account.json'"
    exit 1
fi

FCM_TOKEN="$1"
SERVICE_ACCOUNT_FILE="${2:-./firebase-service-account.json}"
PROJECT_ID="quycky-22841"

# Verificar se o arquivo existe
if [[ ! -f "$SERVICE_ACCOUNT_FILE" ]]; then
    echo -e "${RED}❌ Arquivo Service Account não encontrado: $SERVICE_ACCOUNT_FILE${NC}"
    echo ""
    echo -e "${YELLOW}📋 Para criar o arquivo:${NC}"
    echo "1. Vá para Firebase Console > Project Settings"
    echo "2. Aba 'Service Accounts'"
    echo "3. Clique em 'Generate new private key'"
    echo "4. Salve como 'firebase-service-account.json' nesta pasta"
    echo ""
    echo "Ou especifique o caminho:"
    echo "  $0 \"$FCM_TOKEN\" \"/caminho/para/service-account.json\""
    exit 1
fi

# Verificar se jq está instalado
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ jq não está instalado${NC}"
    echo ""
    echo "Instale com:"
    echo "  brew install jq"
    echo ""
    echo "Ou use o método manual (vou criar um script alternativo)..."
    exit 1
fi

echo -e "${YELLOW}🔑 Gerando JWT token...${NC}"

# Extrair informações do service account
CLIENT_EMAIL=$(jq -r '.client_email' "$SERVICE_ACCOUNT_FILE")
PRIVATE_KEY=$(jq -r '.private_key' "$SERVICE_ACCOUNT_FILE")
PRIVATE_KEY_ID=$(jq -r '.private_key_id' "$SERVICE_ACCOUNT_FILE")

if [[ "$CLIENT_EMAIL" == "null" || "$PRIVATE_KEY" == "null" ]]; then
    echo -e "${RED}❌ Arquivo Service Account inválido${NC}"
    exit 1
fi

echo "Service Account: $CLIENT_EMAIL"

# Criar JWT Header
JWT_HEADER=$(echo -n '{"alg":"RS256","typ":"JWT","kid":"'$PRIVATE_KEY_ID'"}' | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

# Criar JWT Payload
NOW=$(date +%s)
EXP=$((NOW + 3600))
JWT_PAYLOAD=$(echo -n '{"iss":"'$CLIENT_EMAIL'","scope":"https://www.googleapis.com/auth/cloud-platform","aud":"https://oauth2.googleapis.com/token","exp":'$EXP',"iat":'$NOW'}' | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

# Criar assinatura (isso é complexo sem openssl, vou usar um método alternativo)
JWT_UNSIGNED="$JWT_HEADER.$JWT_PAYLOAD"

# Salvar private key em arquivo temporário
TEMP_KEY=$(mktemp)
echo "$PRIVATE_KEY" > "$TEMP_KEY"

# Criar assinatura
SIGNATURE=$(echo -n "$JWT_UNSIGNED" | openssl dgst -sha256 -sign "$TEMP_KEY" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

# Limpar arquivo temporário
rm "$TEMP_KEY"

# JWT completo
JWT="$JWT_UNSIGNED.$SIGNATURE"

echo -e "${BLUE}🌐 Obtendo access token...${NC}"

# Trocar JWT por access token
TOKEN_RESPONSE=$(curl -s -X POST "https://oauth2.googleapis.com/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=$JWT")

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')

if [[ "$ACCESS_TOKEN" == "null" || -z "$ACCESS_TOKEN" ]]; then
    echo -e "${RED}❌ Erro ao obter access token${NC}"
    echo "Resposta: $TOKEN_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Access token obtido!${NC}"

# Payload da notificação
PAYLOAD='{
  "message": {
    "token": "'$FCM_TOKEN'",
    "notification": {
      "body": "Glória a Deus!!!\\o/",
      "title": "Other Test 002"
    },
      "data": {
        "type": "APP_NOTIFICATION",
        "data": "{\"title\":\"Other\",\"message\":\"Mensagem de teste\",\"content\":{\"go_to_page\":\"lobby\",\"open_url\":\"\"}}"
      },
    "apns": {
      "payload": {
        "aps": {
          "content-available": 1,
          "badge": 1
        }
      }
    }
  }
}
'

echo -e "${YELLOW}📱 Enviando notificação...${NC}"

# Enviar notificação
RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d "$PAYLOAD" \
    "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send")

HTTP_CODE=$(echo "$RESPONSE" | tail -n 1)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '$d')

if [[ "$HTTP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Notificação enviada com sucesso!${NC}"
    echo -e "${GREEN}Resposta: ${NC}$RESPONSE_BODY"
    echo ""
    echo -e "${BLUE}🔍 Verificações:${NC}"
    echo "1. Verifique se o badge apareceu no ícone do app"
    echo "2. Monitore os logs do Flutter para background handler"
else
    echo -e "${RED}❌ Erro: HTTP $HTTP_CODE${NC}"
    echo -e "${RED}Resposta: ${NC}$RESPONSE_BODY"
fi
