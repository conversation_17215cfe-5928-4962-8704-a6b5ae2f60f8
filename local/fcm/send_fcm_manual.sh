#!/bin/bash

# Script manual para FCM v1 (requer access token)

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

if [[ -z "$1" ]]; then
    echo -e "${RED}❌ Uso: $0 FCM_TOKEN ACCESS_TOKEN${NC}"
    echo ""
    echo -e "${YELLOW}Para obter ACCESS_TOKEN:${NC}"
    echo "1. Instale gcloud: brew install google-cloud-sdk"
    echo "2. Autentique: gcloud auth application-default login"
    echo "3. Obtenha token: gcloud auth application-default print-access-token"
    echo "4. Use: $0 \"FCM_TOKEN\" \"ACCESS_TOKEN\""
    exit 1
fi

FCM_TOKEN="$1"
ACCESS_TOKEN="$2"
PROJECT_ID="quycky-22841"

if [[ -z "$ACCESS_TOKEN" ]]; then
    echo -e "${RED}❌ Access token é obrigatório${NC}"
    exit 1
fi

echo -e "${YELLOW}📱 Enviando notificação FCM v1...${NC}"

PAYLOAD='{
    "message": {
        "token": "'$FCM_TOKEN'",
        "data": {
            "type": "INVITE_GAME_ROOM_PUSH_MESSAGE",
            "user_name": "Test User",
            "message": "Teste FCM v1 manual"
        },
        "apns": {
            "payload": {
                "aps": {
                    "content-available": 1,
                    "badge": 1
                }
            }
        }
    }
}'

RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d "$PAYLOAD" \
    "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send")

HTTP_CODE=$(echo "$RESPONSE" | tail -n 1)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '$d')

if [[ "$HTTP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Notificação enviada!${NC}"
    echo -e "${GREEN}Resposta: ${NC}$RESPONSE_BODY"
else
    echo -e "${RED}❌ Erro: HTTP $HTTP_CODE${NC}"
    echo -e "${RED}Resposta: ${NC}$RESPONSE_BODY"
fi
