# 🔔 Guia de Teste de Notificações Push - Quycky App

> **📁 Todos os scripts estão na pasta `fcm/`**

## 📋 Pré-requisitos

1. **Server Key do Firebase**:
   - Vá para [Firebase Console](https://console.firebase.google.com)
   - Selecione seu projeto `quycky-22841`
   - Vá em **Project Settings** > **Cloud Messaging**
   - Copie a **Server Key** (Legacy)

2. **FCM Token do dispositivo**:
   - Execute o app no dispositivo físico
   - Procure nos logs por: `🔔 FCM Token: ...`
   - Copie o token completo

## 🚀 Scripts Disponíveis

### 1. Teste Rápido (`quick_test.sh`)

Script simples para teste básico:

```bash
# Configurar a Server Key (apenas uma vez)
nano quick_test.sh
# Substitua YOUR_FIREBASE_SERVER_KEY_HERE pela sua chave

# Executar teste
./quick_test.sh "SEU_FCM_TOKEN_AQUI"
```

### 2. <PERSON><PERSON><PERSON>mpleto (`send_notification.sh`)

Script avançado com múltiplas opções:

```bash
# Configurar a Server Key (apenas uma vez)
nano send_notification.sh
# Substitua YOUR_FIREBASE_SERVER_KEY_HERE pela sua chave

# Teste simples
./send_notification.sh -t "SEU_FCM_TOKEN_AQUI" --test

# Notificação silent (apenas badge, sem alerta)
./send_notification.sh -t "SEU_FCM_TOKEN_AQUI" --silent --badge 5

# Convite para jogo com mensagem personalizada
./send_notification.sh -t "SEU_FCM_TOKEN_AQUI" -u "João" -m "Vamos jogar!"

# Ver todas as opções
./send_notification.sh --help
```

## 🔧 Como Testar

### Passo 1: Preparar o App
1. Execute o app no dispositivo físico
2. Conceda permissões de notificação
3. Copie o FCM Token dos logs
4. **Feche completamente o app** (não apenas minimize)

### Passo 2: Configurar o Script
1. Edite o script escolhido
2. Substitua `YOUR_FIREBASE_SERVER_KEY_HERE` pela sua Server Key
3. Salve o arquivo

### Passo 3: Enviar Notificação
```bash
./quick_test.sh "dA1B2c3D4e5F6g7H8i9J0k1L2m3N4o5P6q7R8s9T0u1V2w3X4y5Z6"
```

### Passo 4: Verificar Resultado
- ✅ **Badge aparece** no ícone do app
- ✅ **Logs mostram** execução do background handler
- ✅ **Badge desaparece** quando o app é aberto

## 📱 Configurações do iOS

Certifique-se de que:

1. **Background App Refresh** está habilitado:
   - Configurações > Geral > Atualização em Segundo Plano
   - Habilite para o app Quycky

2. **Notificações** estão habilitadas:
   - Configurações > Notificações > Quycky
   - Habilite "Badges"

3. **Dispositivo físico**:
   - Notificações em background não funcionam no simulador

## 🔍 Logs para Monitorar

Execute `flutter logs` e procure por:

```
🔔 FCM Token: [token]
🔔 Notification Settings:
  - Authorization Status: AuthorizationStatus.authorized
  - Badge Setting: AppleNotificationSetting.enabled

🔔 [BACKGROUND] Message received:
  - Message ID: [id]
  - Content Available: true
🔔 Badge updated successfully
🔔 Background handler completed
```

## 🚨 Troubleshooting

### Badge não aparece:
- Verifique se o app está completamente fechado
- Confirme que Background App Refresh está habilitado
- Teste em dispositivo físico (não simulador)

### Background handler não executa:
- Verifique se `content-available: 1` está no payload
- Confirme que não há campo `notification` no payload para silent push
- Verifique se a Server Key está correta

### Erro de autenticação:
- Confirme que a Server Key está correta
- Verifique se o projeto Firebase está ativo

## 📄 Exemplo de Payload

Para referência, este é o payload usado para silent push:

```json
{
  "to": "FCM_TOKEN",
  "content_available": true,
  "priority": "high",
  "data": {
    "type": "INVITE_GAME_ROOM_PUSH_MESSAGE",
    "user": {
      "name": "Test User"
    }
  },
  "apns": {
    "payload": {
      "aps": {
        "content-available": 1,
        "badge": 1
      }
    }
  }
}
```

## 🎯 Próximos Passos

Após confirmar que funciona:
1. Integre com seu backend
2. Use o mesmo formato de payload
3. Teste com diferentes tipos de notificação
4. Implemente lógica de contagem de badge mais sofisticada
